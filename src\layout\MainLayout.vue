<template>
  <a-layout class="dashboard">
    <!-- 左侧导航 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="280"
      :collapsed-width="80"
      class="sidebar"
      :class="{ 'mobile-sidebar': isMobile }"
      :style="{ 
        position: isMobile ? 'fixed' : 'relative',
        zIndex: isMobile ? 1000 : 'auto',
        height: isMobile ? '100vh' : 'auto',
        left: isMobile && collapsed ? '-280px' : '0'
      }"
    >
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="brand">
          <div class="brand-icon">iB</div>
          <span v-show="!collapsed" class="brand-name">iBanKo</span>
        </div>
      </div>

      <!-- 用户信息 -->
      <div v-show="!collapsed" class="user-profile">
        <a-avatar :size="48" class="user-avatar">
          <template #icon><UserOutlined /></template>
        </a-avatar>
        <div class="user-info">
          <div class="name"><PERSON></div>
          <div class="role">UX/UI Design</div>
        </div>
      </div>

      <!-- 多层级菜单 -->
      <div class="nav-section">
        <a-menu
          v-model:selectedKeys="selectedKeys"
          v-model:openKeys="openKeys"
          mode="inline"
          :inline-collapsed="collapsed"
          class="sidebar-menu"
        >
          <a-menu-item key="overview">
            <template #icon><DashboardOutlined /></template>
            <span>Overview</span>
          </a-menu-item>
          
          <a-sub-menu key="financial" title="Financial">
            <template #icon><BankOutlined /></template>
            <a-menu-item key="savings">Savings</a-menu-item>
            <a-menu-item key="investments">Investments</a-menu-item>
            <a-menu-item key="loans">Loans</a-menu-item>
          </a-sub-menu>
          
          <a-sub-menu key="cards" title="Cards & Payments">
            <template #icon><CreditCardOutlined /></template>
            <a-menu-item key="credit-cards">Credit Cards</a-menu-item>
            <a-menu-item key="debit-cards">Debit Cards</a-menu-item>
            <a-menu-item key="payments">Payments</a-menu-item>
            <a-menu-item key="transfers">Transfers</a-menu-item>
          </a-sub-menu>
          
          <a-sub-menu key="analytics" title="Analytics">
            <template #icon><LineChartOutlined /></template>
            <a-menu-item key="reports">Reports</a-menu-item>
            <a-menu-item key="activity">Activity</a-menu-item>
            <a-menu-item key="insights">Insights</a-menu-item>
          </a-sub-menu>
          
          <a-menu-item key="profile">
            <template #icon><UserOutlined /></template>
            <span>Profile</span>
          </a-menu-item>
        </a-menu>
      </div>

      <!-- 底部区域 -->
      <div class="sidebar-footer">
        <a-menu mode="inline" :inline-collapsed="collapsed" class="bottom-menu">
          <a-menu-item key="support">
            <template #icon><QuestionCircleOutlined /></template>
            <span>Support</span>
          </a-menu-item>
          <a-menu-item key="settings">
            <template #icon><SettingOutlined /></template>
            <span>Settings</span>
          </a-menu-item>
        </a-menu>
        
        <div v-show="!collapsed" class="theme-toggle">
          <span>Dark Mode</span>
          <a-switch v-model:checked="darkMode" size="small" />
        </div>
        
        <!-- 系统版本信息 -->
        <div v-show="!collapsed" class="version-info">
          <div class="version-item">
            <span class="version-label">Version</span>
            <span class="version-value">v2.1.0</span>
          </div>
          <div class="version-item">
            <span class="version-label">Build</span>
            <span class="version-value">2024.01</span>
          </div>
          <div class="version-item">
            <span class="version-label">Status</span>
            <a-tag color="success" size="small">Stable</a-tag>
          </div>
        </div>
        
        <a-button type="text" class="sign-out" @click="handleSignOut">
          <template #icon><LogoutOutlined /></template>
          <span v-show="!collapsed">Sign out</span>
        </a-button>
      </div>
    </a-layout-sider>

    <!-- 遮罩层 (移动端) -->
    <div 
      v-if="isMobile && !collapsed" 
      class="sidebar-overlay"
      @click="collapsed = true"
    ></div>

    <!-- 主内容区域 -->
    <a-layout class="main-layout">
      <!-- 顶部栏 -->
      <a-layout-header class="header">
        <div class="header-left">
          <!-- 菜单切换按钮 -->
          <a-button
            type="text"
            :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="toggleSidebar"
            class="menu-trigger"
          />
          
          <!-- 搜索框 -->
          <div class="search-container">
            <a-input-search
              v-model:value="searchValue"
              placeholder="Search..."
              :style="{ width: searchWidth }"
              size="middle"
              @search="onSearch"
              @focus="onSearchFocus"
              @blur="onSearchBlur"
            />
          </div>
        </div>
        
        <div class="header-right">
          <!-- 账户选择 (桌面端显示) -->
          <a-select 
            v-if="!isMobile"
            v-model:value="selectedAccount" 
            placeholder="Choose Account" 
            style="width: 160px"
            size="middle"
          >
            <a-select-option value="main">Main Account</a-select-option>
            <a-select-option value="savings">Savings Account</a-select-option>
            <a-select-option value="business">Business Account</a-select-option>
          </a-select>
          
          <!-- 通知 -->
          <a-badge :count="3" size="small">
            <a-button type="text" shape="circle" size="large">
              <template #icon><BellOutlined /></template>
            </a-button>
          </a-badge>
          
          <!-- 用户头像下拉菜单 -->
          <a-dropdown :trigger="['hover', 'click']" placement="bottomRight">
            <a-avatar :size="36" class="user-avatar-header">
              <template #icon><UserOutlined /></template>
            </a-avatar>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <template #icon><UserOutlined /></template>
                  My Profile
                </a-menu-item>
                <a-menu-item key="account">
                  <template #icon><SettingOutlined /></template>
                  Account Settings
                </a-menu-item>
                <a-menu-item key="security">
                  <template #icon><SafetyOutlined /></template>
                  Security
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="help">
                  <template #icon><QuestionCircleOutlined /></template>
                  Help & Support
                </a-menu-item>
                <a-menu-item key="logout" @click="handleSignOut">
                  <template #icon><LogoutOutlined /></template>
                  Sign Out
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <div class="content-grid">
            <!-- 左侧主要内容 -->
            <div class="main-content">
              <!-- 银行卡 -->
              <a-card class="card-section" :bordered="false">
                <div class="card-header">
                  <span class="card-title">My Card</span>
                  <a-tag color="success" class="trend-tag">
                    <template #icon><ArrowUpOutlined /></template>
                    10%
                  </a-tag>
                </div>
                <div class="balance">$1,43,899.00</div>
                <div class="card-actions">
                  <a-button type="primary" :size="isMobile ? 'middle' : 'large'" class="action-btn">
                    <template #icon><ArrowDownOutlined /></template>
                    Deposit
                  </a-button>
                  <a-button :size="isMobile ? 'middle' : 'large'" class="action-btn">
                    <template #icon><ArrowUpOutlined /></template>
                    Withdraw
                  </a-button>
                </div>
                <div class="card-chart"></div>
              </a-card>

              <!-- 财务概览 -->
              <a-card title="Financial Record" :bordered="false" class="financial-overview">
                <template #extra>
                  <a-select defaultValue="Month" size="small">
                    <a-select-option value="Month">Month</a-select-option>
                    <a-select-option value="Quarter">Quarter</a-select-option>
                    <a-select-option value="Year">Year</a-select-option>
                  </a-select>
                </template>
                
                <a-row :gutter="[16, 16]">
                  <a-col :xs="24" :sm="8">
                    <a-card size="small" class="stat-card income">
                      <a-statistic
                        title="Total Income"
                        :value="85992"
                        :precision="0"
                        suffix="USD"
                        :value-style="{ color: '#52c41a', fontSize: isMobile ? '20px' : '24px' }"
                      >
                        <template #prefix><ArrowUpOutlined /></template>
                      </a-statistic>
                      <div class="stat-change">+17% from last month</div>
                    </a-card>
                  </a-col>
                  <a-col :xs="24" :sm="8">
                    <a-card size="small" class="stat-card expense">
                      <a-statistic
                        title="Total Expense"
                        :value="38160"
                        :precision="0"
                        suffix="USD"
                        :value-style="{ color: '#ff4d4f', fontSize: isMobile ? '20px' : '24px' }"
                      >
                        <template #prefix><ArrowDownOutlined /></template>
                      </a-statistic>
                      <div class="stat-change">+44% from last month</div>
                    </a-card>
                  </a-col>
                  <a-col :xs="24" :sm="8">
                    <a-card size="small" class="stat-card saving">
                      <a-statistic
                        title="Total Saving"
                        :value="47832"
                        :precision="0"
                        suffix="USD"
                        :value-style="{ color: '#1890ff', fontSize: isMobile ? '20px' : '24px' }"
                      >
                        <template #prefix><BankOutlined /></template>
                      </a-statistic>
                      <div class="stat-change">+45% from last month</div>
                    </a-card>
                  </a-col>
                </a-row>
              </a-card>

              <!-- 资金流向 -->
              <a-card title="Money Flow" :bordered="false" class="money-flow">
                <template #extra>
                  <div class="flow-controls">
                    <a-tag color="blue" v-if="!isMobile">Total Saving</a-tag>
                    <a-tag color="orange" v-if="!isMobile">Total Expense</a-tag>
                    <a-select defaultValue="Weekly" size="small">
                      <a-select-option value="Weekly">Weekly</a-select-option>
                      <a-select-option value="Monthly">Monthly</a-select-option>
                    </a-select>
                  </div>
                </template>
                
                <div class="chart-container">
                  <div class="flow-chart">
                    <a-tag color="processing" class="chart-label">$2,289</a-tag>
                  </div>
                </div>
              </a-card>

              <!-- 底部功能 -->
              <a-row :gutter="[16, 16]">
                <a-col :xs="24" :lg="12">
                  <a-card title="Send Money To" size="small" :bordered="false">
                    <template #extra>
                      <a-button type="text" size="small">
                        <template #icon><MoreOutlined /></template>
                      </a-button>
                    </template>
                    
                    <div class="avatars">
                      <a-button type="primary" shape="circle" :size="isMobile ? 'default' : 'large'">
                        <template #icon><PlusOutlined /></template>
                      </a-button>
                      <a-avatar-group :max-count="3" :size="isMobile ? 'default' : 'large'">
                        <a-avatar>
                          <template #icon><UserOutlined /></template>
                        </a-avatar>
                        <a-avatar>
                          <template #icon><UserOutlined /></template>
                        </a-avatar>
                        <a-avatar>
                          <template #icon><UserOutlined /></template>
                        </a-avatar>
                      </a-avatar-group>
                    </div>
                  </a-card>
                </a-col>
                
                <a-col :xs="24" :lg="12">
                  <a-card title="Scheduled Payments" size="small" :bordered="false">
                    <template #extra>
                      <a-button type="text" size="small">
                        <template #icon><MoreOutlined /></template>
                      </a-button>
                    </template>
                    
                    <a-list :data-source="scheduledPayments" size="small">
                      <template #renderItem="{ item }">
                        <a-list-item>
                          <a-list-item-meta>
                            <template #avatar>
                              <a-avatar :style="{ backgroundColor: item.color }" size="small">
                                {{ item.name.charAt(0) }}
                              </a-avatar>
                            </template>
                            <template #title>{{ item.name }}</template>
                          </a-list-item-meta>
                          <div class="payment-amount">{{ item.amount }}</div>
                        </a-list-item>
                      </template>
                    </a-list>
                  </a-card>
                </a-col>
              </a-row>
            </div>

            <!-- 右侧面板 -->
            <div class="sidebar-right" v-if="!isMobile">
              <!-- 交易记录 -->
              <a-card title="Transactions" :bordered="false">
                <template #extra>
                  <a-select defaultValue="Month" size="small">
                    <a-select-option value="Month">Month</a-select-option>
                    <a-select-option value="Week">Week</a-select-option>
                  </a-select>
                </template>
                
                <a-list :data-source="transactions" size="small">
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <a-avatar size="small">
                            <template #icon><UserOutlined /></template>
                          </a-avatar>
                        </template>
                        <template #title>{{ item.name }}</template>
                        <template #description>{{ item.date }}</template>
                      </a-list-item-meta>
                      <div :class="['trans-amount', item.type]">
                        {{ item.type === 'income' ? '+' : '-' }}{{ item.amount }}
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </a-card>

              <!-- 可用卡片 -->
              <a-card title="Available Cards" :bordered="false">
                <template #extra>
                  <a-button type="link" size="small">View all</a-button>
                </template>
                
                <div class="cards">
                  <a-card size="small" class="credit-card visa">
                    <div class="card-top">
                      <div class="card-balance">$3,736</div>
                      <div class="card-brand">VISA</div>
                    </div>
                    <div class="card-info">
                      <a-typography-text type="secondary" class="card-label">Card Number</a-typography-text>
                      <div class="card-number">3253 2323 7319 ****</div>
                      <a-typography-text type="secondary" class="card-label">Exp</a-typography-text>
                      <div class="card-exp">**/**</div>
                    </div>
                  </a-card>
                  
                  <a-card size="small" class="credit-card amex">
                    <div class="card-top">
                      <div class="card-balance">$21,426</div>
                      <div class="card-brand">AMEX</div>
                    </div>
                    <div class="card-info">
                      <a-typography-text type="secondary" class="card-label">Card Number</a-typography-text>
                      <div class="card-number">3253 8243 1100 ****</div>
                      <a-typography-text type="secondary" class="card-label">Exp</a-typography-text>
                      <div class="card-exp">**/**</div>
                    </div>
                  </a-card>
                </div>
              </a-card>
            </div>
          </div>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, h } from 'vue'
import {
  DashboardOutlined,
  BankOutlined,
  CreditCardOutlined,
  LineChartOutlined,
  UserOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  SafetyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MoreOutlined,
  PlusOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref(['overview'])
const openKeys = ref(['financial'])
const darkMode = ref(false)
const selectedAccount = ref('main')
const searchValue = ref('')
const searchFocused = ref(false)
const windowWidth = ref(window.innerWidth)

// 计算属性
const isMobile = computed(() => windowWidth.value < 768)
const searchWidth = computed(() => {
  if (isMobile.value) {
    return searchFocused.value ? '200px' : '120px'
  }
  return '300px'
})

// 数据
const scheduledPayments = [
  { id: 1, name: 'Discord', amount: '$34.99/m', color: '#5865F2' },
  { id: 2, name: 'Wattpad', amount: '$14.99/m', color: '#FF6500' },
  { id: 3, name: 'Netflix', amount: '$9.99/m', color: '#E50914' }
]

const transactions = [
  { id: 1, name: 'Jane Cooper', date: '09 Sep, 2022', amount: '$1200', type: 'income' },
  { id: 2, name: 'Leslie Alexander', date: '09 Sep, 2022', amount: '$1750', type: 'income' },
  { id: 3, name: 'Flight Ticket', date: '08 Sep, 2022', amount: '$500', type: 'expense' },
  { id: 4, name: 'Robert Fox', date: '08 Sep, 2022', amount: '$4300', type: 'income' },
  { id: 5, name: 'KFC', date: '08 Sep, 2022', amount: '$189', type: 'expense' },
  { id: 6, name: 'Jacob Jones', date: '07 Sep, 2022', amount: '$840', type: 'income' }
]

// 方法
const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const onSearch = (value) => {
  console.log('Search:', value)
}

const onSearchFocus = () => {
  searchFocused.value = true
}

const onSearchBlur = () => {
  searchFocused.value = false
}

const handleSignOut = () => {
  console.log('Sign out')
}

const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (isMobile.value) {
    collapsed.value = true
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  if (isMobile.value) {
    collapsed.value = true
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
  background: #f0f2f5;
}

/* 侧边栏 */
.sidebar {
  background: #ffffff;
  border-right: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.mobile-sidebar {
  transition: left 0.3s ease;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 999;
}

.logo-section {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-icon {
  width: 36px;
  height: 36px;
  background: #1890ff;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
}

.brand-name {
  font-size: 20px;
  font-weight: 700;
  color: #262626;
  white-space: nowrap;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  background: #f0f0f0;
  color: #8c8c8c;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.name {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.role {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-section {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.sidebar-menu :deep(.ant-menu-item),
.sidebar-menu :deep(.ant-menu-submenu-title) {
  margin: 2px 16px;
  width: calc(100% - 32px);
  border-radius: 6px;
}

.sidebar-menu :deep(.ant-menu-item-selected) {
  background: #e6f7ff;
  color: #1890ff;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.bottom-menu {
  border: none;
  background: transparent;
  margin-bottom: 16px;
}

.bottom-menu :deep(.ant-menu-item) {
  margin: 2px 0;
  border-radius: 6px;
}

.theme-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  background: #fafafa;
  border-radius: 6px;
  font-size: 14px;
}

.version-info {
  background: #f6f8fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.version-item:last-child {
  margin-bottom: 0;
}

.version-label {
  font-size: 11px;
  color: #8c8c8c;
}

.version-value {
  font-size: 11px;
  font-weight: 500;
  color: #262626;
}

.sign-out {
  width: 100%;
  justify-content: flex-start;
  color: #8c8c8c;
}

/* 主布局 */
.main-layout {
  background: #f0f2f5;
}

.header {
  background: #ffffff;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.menu-trigger {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-container {
  flex: 1;
  max-width: 400px;
}

.search-container :deep(.ant-input-search) {
  transition: width 0.3s ease;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-header {
  cursor: pointer;
  background: #f0f0f0;
  color: #8c8c8c;
  transition: all 0.3s;
}

.user-avatar-header:hover {
  transform: scale(1.05);
}

/* 内容区域 */
.content {
  padding: 0;
  overflow-y: auto;
}

.content-wrapper {
  padding: 24px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 银行卡片 */
.card-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.card-section :deep(.ant-card-body) {
  padding: 32px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: white;
}

.trend-tag {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
}

.balance {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 24px;
  color: white;
}

.card-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  min-width: 100px;
}

.card-chart {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120px;
  height: 60px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
  border-radius: 16px 0 16px 0;
}

/* 财务概览 */
.financial-overview :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

.stat-card {
  border-radius: 12px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.income {
  background: linear-gradient(135deg, #f0fff4, #f6ffed);
  border: 1px solid #b7eb8f;
}

.stat-card.expense {
  background: linear-gradient(135deg, #fff2f0, #fff1f0);
  border: 1px solid #ffccc7;
}

.stat-card.saving {
  background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
  border: 1px solid #91d5ff;
}

.stat-change {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 8px;
}

/* 资金流向 */
.flow-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.chart-container {
  margin-top: 24px;
}

.flow-chart {
  height: 160px;
  background: linear-gradient(135deg, #f6f8fa, #e1e8ed);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chart-label {
  font-size: 16px;
  font-weight: 600;
}

/* 底部功能区 */
.avatars {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-amount {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

/* 右侧面板 */
.sidebar-right {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.trans-amount {
  font-weight: 600;
  font-size: 14px;
}

.trans-amount.income {
  color: #52c41a;
}

.trans-amount.expense {
  color: #ff4d4f;
}

/* 信用卡 */
.cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.credit-card {
  border-radius: 12px;
  overflow: hidden;
}

.credit-card.visa {
  background: linear-gradient(135deg, #e8eaf6, #c5cae9);
  color: #3f51b5;
}

.credit-card.amex {
  background: linear-gradient(135deg, #263238, #37474f);
  color: white;
}

.card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-balance {
  font-size: 18px;
  font-weight: 700;
}

.card-brand {
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1px;
}

.card-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.card-label {
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.card-number,
.card-exp {
  font-family: 'SF Mono', 'Monaco', monospace;
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .balance {
    font-size: 28px;
  }
  
  .card-section :deep(.ant-card-body) {
    padding: 24px;
  }
  
  .flow-chart {
    height: 120px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 0 12px;
  }
  
  .content-wrapper {
    padding: 12px;
  }
  
  .balance {
    font-size: 24px;
  }
  
  .card-actions {
    justify-content: center;
  }
  
  .action-btn {
    flex: 1;
    min-width: auto;
  }
}
</style>

## ✨ 修复完成的功能：

### 🔧 **1. 去除 Hello Steward**
- 完全移除了顶部的问候语
- 简化了头部布局

### 🎨 **2. Logo 移至菜单栏顶部**
- 将 iB 图标和 iBanKo 品牌名移至侧边栏最上方
- 替换用户头像为通用图标
- 保持品牌标识的突出显示

### 📱 **3. 完整响应式设计**
- **桌面端**: 正常显示所有功能
- **平板端**: 自动调整布局，隐藏右侧面板
- **移动端**: 侧边栏变为抽屉式，添加遮罩层

### 🔍 **4. 菜单切换和搜索**
- 添加菜单展开/收起按钮
- 响应式搜索框（移动端聚焦时扩展）
- 移动端优化的搜索体验

### 📲 **5. 移动端适配**
- 侧边栏在移动端自动收起
- 固定定位的侧边栏和遮罩层
- 触摸友好的按钮尺寸
- 优化的间距和字体大小

### 🎯 **6. 其他优化**
- 保持所有 Ant Design 组件的原生功能
- 平滑的动画过渡效果
- 更好的触摸交互体验
- 自适应的内容布局

现在您可以在不同设备上测试响应式效果，菜单切换功能也完全正常工作！